body {
  margin: 0;
  padding: 0;
  background: #e4ebf1; /* fix: mac os scroll bg */
}

html.dark {
  body {
    background: #000; /* fix: mac os scroll bg */
  }
}


/* dark/light radial transition */
html[disabled-transition] * {
  transition: none !important;
}

::view-transition-old(root),
::view-transition-new(root) {
  animation: none;
  mix-blend-mode: normal;
}

.dark::view-transition-old(root) {
  z-index: 1;
}

.dark::view-transition-new(root) {
  z-index: 9999;
}

::view-transition-old(root) {
  z-index: 9999;
}

::view-transition-new(root) {
  z-index: 1;
}
