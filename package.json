{"name": "react-admin", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:test": "tsc && vite build --mode development", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ant-design/plots": "^2.2.4", "@antv/larkmap": "^1.4.17", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@reduxjs/toolkit": "^2.2.5", "ahooks": "^3.8.0", "antd": "^5.17.4", "antd-style": "^3.6.2", "axios": "^1.7.2", "dayjs": "^1.11.11", "lodash": "^4.17.21", "lucide-react": "^0.379.0", "nprogress": "^0.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^9.1.2", "react-router-dom": "^6.23.1"}, "devDependencies": {"@types/lodash": "^4.17.4", "@types/node": "^20.12.12", "@types/nprogress": "^0.2.3", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "axios-mock-adapter": "^1.22.0", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "less": "^4.2.0", "prettier": "^3.2.5", "prettier-plugin-organize-imports": "^3.2.4", "typescript": "^5.2.2", "vite": "^5.2.0"}}